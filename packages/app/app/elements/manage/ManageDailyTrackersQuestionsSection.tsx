import ManageDailyQuestion from "@components/manage/ManageDailyQuestion";
import { trpc } from "@providers/RootProvider";
import _ from "lodash";
import { endOfDay, isBefore, isToday, startOfDay } from "date-fns";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { Dimensions, FlatList, View } from "react-native";
import { RouterOutput } from "../../../../shared";
import {
  ActionTypes,
  Option,
  Question,
  QuestionTypes,
  Topic,
} from "../../../../shared/types/manage";
import ManageCompletedQuestion from "@components/manage/ManageCompletedQuestion";
import LoadingScreen from "@components/common/LoadingScreen";
import { openLinkingUrl } from "../../screens/profile/ContactUs";
import ManageAirQualityIndex from "@screens/manage/ManageAirQualityIndex";
import NotificationBelle from "@screens/manage/NotificationBelle";
import ManageProgressAndCalendarCard from "./ManageProgressAndCalenderCard";
import Pressable from "@components/common/Pressable";
import AppText from "@components/common/AppText";
import mixpanel from "@utils/mixpanel";
import { useSession } from "@hooks/persistUser";

const SCREEN_WIDTH = Dimensions.get("screen").width;
const COMPONENT_WIDTH = SCREEN_WIDTH - 16 * 2;

const totalDailyQuestion = 12;

function processQuestion(questions: Question[], isCarePartner: boolean) {
  return _.map(questions, (tq) => {
    if (isCarePartner) {
      const { carePartnerText, options = [] } = tq;
      return {
        ...tq,
        question: carePartnerText?.toString() || tq.question.toString(),
        options: options.map((option: Option) => ({
          ...option,
          value: option.carePartnerValue || option.value,
        })),
      };
    }
    return tq;
  });
}

function ManageDailyTrackersQuestionsSection({
  topics,
  onSuccessAction,
  isCarePartner,
  selectedWeekDay,
  onCompletedAction,
  onAnimationComplete,
  isOnboardingCheckIn = false,
  setDailyCheckInCompleted,
  lat,
  lng,
  streak,
}: {
  topics: RouterOutput["manage"]["getTopics"];
  selectedWeekDay: Date;
  isCarePartner: boolean;
  onAnimationComplete?: () => void;
  onSuccessAction?: () => void;
  onCompletedAction?: () => void;
  isOnboardingCheckIn?: boolean;
  setDailyCheckInCompleted?: (v: boolean) => void;
  lat?: number;
  lng?: number;
  streak?: any;
}) {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [subText, setSubText] = useState("");
  const [loading, setLoading] = useState(false);
  const [totalQuestion, setTotalQuestion] = useState(0);
  const [showManageCompleted, setShowManageCompleted] = useState(true);
  const userSelectedTopics = useMemo(() => topics[0], [topics]);
  const utils = trpc.useUtils();
  const { user } = useSession();

  let initialRendering = false;
  // Key to optimistically update answers
  const userDailyTrackersKey = {
    startDate: startOfDay(selectedWeekDay),
    endDate: endOfDay(selectedWeekDay),
    topicId: userSelectedTopics?._id.toString(),
  };

  const indexRef = useRef(1);
  const flatlistRef = useRef<FlatList>(null);
  const shouldPrefillQuestions = useRef(true);

  const getNextQuestion = trpc.manage.getNextQuestion.useMutation();
  const getAqi = trpc.lib.aqiData.useMutation();

  const upsertUserDailyTracker =
    trpc.manage.upsertUserDailyTracker.useMutation();

  // Clear questions when weekday changes and get the first question
  // useEffect(() => {
  //   setQuestions([]);

  //   (async () => {
  //     const { nextQuestion } = await getNextQuestion.mutateAsync({
  //       selectedWeekDay,
  //     });

  //     if (nextQuestion) {
  //       setQuestions([nextQuestion]);
  //     }
  //   })();
  // }, [String(selectedWeekDay)]);

  // useEffect(() => {
  //   if (isToday(selectedWeekDay)) {
  //     const timer = setTimeout(() => {
  //       console.log("settimeout");
  //       setShowManageCompleted(false);
  //     }, 5000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [selectedWeekDay, questions]);

  // If weekday changes, reset shouldPrefillQuestions
  useEffect(() => {
    shouldPrefillQuestions.current = true;
  }, [selectedWeekDay, topics]);

  // Remove manual refetch as the query will automatically refetch when dependencies change
  useEffect(() => {
    setLoading(true);
  }, [selectedWeekDay, topics]);

  const {
    data: userDailyTrackers,
    isLoading: trackersLoading,
    refetch: refetchTrackers,
  } = trpc.manage.getUserDailyTrackers.useQuery(userDailyTrackersKey, {
    enabled: !!selectedWeekDay && !!topics.length,
  });

  // Handle data processing when userDailyTrackers changes
  useEffect(() => {
    if (!userDailyTrackers || !shouldPrefillQuestions.current) {
      return;
    }

    const processTrackerData = async (data: typeof userDailyTrackers) => {
      try {
        setLoading(true);
        if (!shouldPrefillQuestions.current) return;
        shouldPrefillQuestions.current = false;
        const ACTIVITY_QUESTION_ID = "681a2f558c9ece01dff16bb4";

        // prefill questions
        if (!topics.length) {
          return;
        }

        const scheduleanswers = _.get(data?.[0], "answers", []);

        const filteredActivityAnswers = scheduleanswers.filter(
          (ans) => ans.question === ACTIVITY_QUESTION_ID
        );

        if (!data.length || (data?.length && filteredActivityAnswers?.length)) {
          const result = await getNextQuestion.mutateAsync({
            selectedWeekDay,
            topicId: userSelectedTopics?._id.toString(),
          });

          if (
            result &&
            typeof result === "object" &&
            "nextQuestion" in result &&
            result.nextQuestion
          ) {
            setQuestions([result.nextQuestion as Question]);
          }
          return;
        }
        const [{ answers = [] } = {}] = data;

        let [{ dailyTrackerQuestions }] = topics;

        dailyTrackerQuestions = processQuestion(
          dailyTrackerQuestions,
          isCarePartner
        );

        const answeredQuestionIds = answers.map((a) => a.question);

        const questions = answeredQuestionIds.map((q) =>
          dailyTrackerQuestions.find((dtq) => dtq._id === q)
        );
        const result = await getNextQuestion.mutateAsync({
          selectedWeekDay,
          currentQuestionId: _.get(_.last(answers || []), "question") as string,
          selectedOptionExIds: [
            _.get(_.last(answers || []), "rawOptionData.externalId"),
          ] as [string],
          topicId: userSelectedTopics?._id.toString(),
        });

        const nextQuestion =
          result && typeof result === "object" && "nextQuestion" in result
            ? result.nextQuestion
            : null;
        const isCompletedQuestion =
          nextQuestion?.type === QuestionTypes.Completed;
        if (isCompletedQuestion) {
          setSubText(nextQuestion?.subText || "");
        }
        setQuestions(questions as Question[]);
        flatlistRef.current?.scrollToEnd({ animated: false });
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };

    processTrackerData(userDailyTrackers);
  }, [
    userDailyTrackers,
    selectedWeekDay,
    topics,
    isCarePartner,
    userSelectedTopics,
  ]);

  // prefil questions with answers

  const answers = useMemo(() => {
    return (userDailyTrackers ?? []).map((tracker) => tracker.answers).flat();
  }, [userDailyTrackers]);

  const onValueSelect = async (
    {
      dtq,
      selectedOptions,
    }: {
      dtq: Topic["dailyTrackerQuestions"][number];
      selectedOptions: Option[];
    },
    scrollToNext: boolean = true
  ) => {
    if (!isOnboardingCheckIn) {
      const feedbackProps: Record<string, string> = {};

      // selectedOptions.forEach((opt, index) => {
      //   const key =
      //     index === 0 ? "checkin_feedback" : `checkin_feedback${index}`;
      //   feedbackProps[key] = opt?.value || "";
      // });
      const joinedFeedback = selectedOptions
        .map((opt) => opt?.value || "")
        .join(", ");
      await mixpanel.trackEvent(
        `Daily checkin (Step ${indexRef.current})`,
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          checkin_date: new Date(selectedWeekDay).toISOString() || "",
          checkin_feedback: joinedFeedback || "",
          checkin_topic: JSON.stringify(userSelectedTopics?.name),
          topic_id: userSelectedTopics?._id.toString(),
          steps_count: String(indexRef.current),
          // checkin_feedback: _.join(selectedOptions, ", ")
          // ...feedbackProps
        },
        String(user?._id),
        "v2"
      );
    }
    indexRef.current += 1;
    const dailyQuestionOptionExIds = ["111-55-1", "111-55-2", "111-55-3"];
    const selectedDailyOptionId = _.includes(
      dailyQuestionOptionExIds,
      _.map(selectedOptions, "externalId")[0]
    );
    if (isOnboardingCheckIn && selectedDailyOptionId) {
      mixpanel.trackEvent(
        "Today's Asthma selected(Step 3)(Account Setup)",
        {
          email_or_phone: user?.email || user?.contact?.phone || "",
          asthma_status:
            selectedOptions[selectedOptions.length - 1]?.value || "",
        },
        user?._id?.toString(),
        "v2"
      );
    }
    const selectedQuestionIndex = questions.findIndex((q) => q._id === dtq._id);
    let aqiData = [];
    try {
      aqiData = await getAqi.mutateAsync({
        lat: lat || 0,
        lng: lng || 0,
        date: new Date(selectedWeekDay),
      });
    } catch (error) {
      console.warn("AQI fetch failed, proceeding without AQI", error);
    }
    const currentAnswersToTheSelectedIndex = answers.slice(
      0,
      Math.max(selectedQuestionIndex, 0)
    );
    const isAqiAlreadyExists = answers?.[0]?.hasOwnProperty("aqi");
    const expectedAnswersToUpsert = [
      ...currentAnswersToTheSelectedIndex,
      {
        answerText: "",
        ...(selectedDailyOptionId
          ? {
              aqi: !isAqiAlreadyExists
                ? aqiData?.[0]?.AQI || 0
                : answers?.[0]?.aqi,
            }
          : {}),
        question: dtq._id as string,
        selectedOption: _.map(selectedOptions, "_id") as [string],
        rawOptionData: selectedOptions[selectedOptions.length - 1],
      },
    ];
    // optimistic update answers-list
    utils.manage.getUserDailyTrackers.setData(userDailyTrackersKey, (data) => {
      if (!data) return undefined;

      return data.map((d) => ({ ...d, answers: expectedAnswersToUpsert }));
    });

    const selectedOptionsExternalIds = selectedOptions.map((s) => s.externalId);

    const callAction = dtq.actions?.find(
      (action) =>
        action.actionType === ActionTypes.Call &&
        selectedOptionsExternalIds.includes(action.metadata.optionExternalId)
    );

    if (callAction) {
      return openLinkingUrl("tel:", callAction.metadata.phone);
    }

    if (selectedDailyOptionId) {
      const questionQount = await getNextQuestion.mutateAsync({
        currentQuestionId: String(dtq._id),
        selectedOptionExIds: _.map(selectedOptions, "externalId") as [string],
        selectedWeekDay,
        topicId: userSelectedTopics?._id.toString(),
        getQuestionCount: true,
      });
      setTotalQuestion(questionQount as number);
    }

    const nextQuestionResult = await getNextQuestion.mutateAsync({
      currentQuestionId: String(dtq._id),
      selectedOptionExIds: _.map(selectedOptions, "externalId") as [string],
      selectedWeekDay,
      topicId: userSelectedTopics?._id.toString(),
    });

    const nextQuestionData =
      nextQuestionResult &&
      typeof nextQuestionResult === "object" &&
      "nextQuestion" in nextQuestionResult
        ? nextQuestionResult
        : null;
    const nextQuestion = nextQuestionData?.nextQuestion;
    const showConfetti = nextQuestionData?.showConfetti || false;

    const answersPayload = {
      answers: expectedAnswersToUpsert,
      date: selectedWeekDay,
      topic: topics?.[0]?._id as string,
      completed: nextQuestion?.type === QuestionTypes.Completed,
    };

    // save user selection
    // @ts-expect-error
    await upsertUserDailyTracker.mutateAsync(answersPayload);
    onSuccessAction?.();
    refetchTrackers();

    const isCompletedQuestion = nextQuestion?.type === QuestionTypes.Completed;

    if (isCompletedQuestion && !isOnboardingCheckIn) {
      await mixpanel.trackEvent(
        `Daily checkin completed (Home screen)`,
        {
          email: user?.email || "",
          phone: user?.contact?.phone || "",
          checkin_date: new Date(selectedWeekDay).toISOString(),
          checkin_feedback: selectedOptions[0]?.value || "",
          checkin_topic: JSON.stringify(userSelectedTopics?.name),
          topic_id: userSelectedTopics?._id.toString(),
          steps_count: String(indexRef.current),
        },
        String(user?._id),
        "v2"
      );
      indexRef.current = 1;
      setSubText(nextQuestion?.subText as string);
      if (streak) {
        streak?.refetch();
      }
      // onAnimationComplete?.();
    }

    if (nextQuestion) {
      setQuestions((prev) => [
        ...prev.slice(0, Math.max(selectedQuestionIndex + 1, 1)),
        ...(isCompletedQuestion ? [] : [nextQuestion as Question]),
      ]);
      if (scrollToNext) {
        scrollNextQuestion();
      }
      // setTimeout(() => {
      //   if (scrollToNext) {
      //     scrollNextQuestion();
      //   }
      // }, 500);
    }

    if (showConfetti) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(onCompletedAction?.());
        }, 1000);
      });
    }
  };

  const [canScrollToNextQuestion, setCanScrollToNextQuestion] = useState(false);

  const scrollNextQuestion = () => {
    setTimeout(() => {
      flatlistRef.current?.scrollToEnd({ animated: true });
    }, 200);
  };

  const responsesCompleted = userDailyTrackers?.some(
    (tracker) => tracker.completed
  );

  useEffect(() => {
    if (responsesCompleted && _.isFunction(setDailyCheckInCompleted)) {
      setDailyCheckInCompleted(true);
    }
  }, [responsesCompleted]);

  useEffect(() => {
    if (isToday(selectedWeekDay) && responsesCompleted) {
      const timer = setTimeout(() => {
        setShowManageCompleted(true);
      }, 5000);

      return () => clearTimeout(timer);
    } else {
      setShowManageCompleted(false);
    }
  }, [selectedWeekDay, responsesCompleted]);

  useEffect(() => {
    initialRendering = true;
  }, [selectedWeekDay, topics]); // Add dependencies to prevent infinite re-renders

  if (trackersLoading || loading) {
    return (
      <View
        className="mt-4"
        style={{
          width: SCREEN_WIDTH,
          alignItems: "center",
        }}
      >
        <View
          className="bg-white rounded-2xl p-4 h-[300px] shadow-md flex flex-col shadow-primary/10"
          style={{ width: COMPONENT_WIDTH }}
        >
          <LoadingScreen />
        </View>
      </View>
    );
  }

  const renderAQI = () => {
    return (
      <View className="mr-4">
        <NotificationBelle />
        <ManageProgressAndCalendarCard selectedTopic={userSelectedTopics} />
        <ManageAirQualityIndex />
        <Pressable
          onPress={() => {
            // optimistically update completed to false
            utils.manage.getUserDailyTrackers.setData(
              userDailyTrackersKey,
              (data) => {
                if (!data) return undefined;
                return data.map((d) => ({ ...d, completed: false }));
              }
            );
          }}
          style={{ backgroundColor: "#0B79D3" }}
          className="bg-[rgba(245,247,249,0.20)] flex justify-center items-center p-2 rounded-lg"
        >
          <AppText className="text-[16px] text-white font-montserratSemiBold">
            Edit Responses
          </AppText>
        </Pressable>
      </View>
    );
  };

  if (responsesCompleted && !isOnboardingCheckIn) {
    const today = startOfDay(new Date());
    const isPastDate = isBefore(selectedWeekDay, today);
    if (isPastDate) {
      return (
        <View className="mt-4 ml-4">
          <ManageCompletedQuestion
            onEditResponseClick={() => {
              // optimistically update completed to false
              utils.manage.getUserDailyTrackers.setData(
                userDailyTrackersKey,
                (data) => {
                  if (!data) return undefined;
                  return data.map((d) => ({ ...d, completed: false }));
                }
              );
            }}
            subText={subText}
          />
        </View>
      );
    }
    return (
      <View className="mt-4 ml-4">
        {isToday(selectedWeekDay) && showManageCompleted ? (
          renderAQI()
        ) : (
          <ManageCompletedQuestion
            onEditResponseClick={() => {
              // optimistically update completed to false
              utils.manage.getUserDailyTrackers.setData(
                userDailyTrackersKey,
                (data) => {
                  if (!data) return undefined;
                  return data.map((d) => ({ ...d, completed: false }));
                }
              );
            }}
            subText={subText}
          />
        )}
      </View>
    );
  }
  return (
    <FlatList
      overScrollMode="never"
      bounces={false}
      pagingEnabled
      contentContainerStyle={{ paddingBottom: 32, marginTop: 16 }}
      horizontal
      ref={flatlistRef}
      data={questions}
      keyExtractor={(_, idx) => String(idx)}
      renderItem={({ item: q, index }) => {
        const selectedAnswer = answers.find(
          (answer) => answer.question === q._id
        );
        const progressQuestion = (index / totalQuestion) * 100;
        return (
          <View className="">
            <ManageDailyQuestion
              topic={topics?.[0]?.name}
              dailyTrackerQuestion={q}
              onValueSelect={onValueSelect}
              selectedOptions={selectedAnswer?.selectedOption}
              loading={loading}
              userSelectedTopic={userSelectedTopics?._id}
              isOnboardingCheckIn={isOnboardingCheckIn}
              onAnimationComplete={() => {
                onAnimationComplete?.();
                scrollNextQuestion();
              }}
              progressQuestion={progressQuestion}
            />
          </View>
        );
      }}
    />
  );
}

export default React.memo(ManageDailyTrackersQuestionsSection);
