"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import <PERSON>Field from "@web/components/form/FormField";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";
import { FormSelect } from "@web/components/form/FormSelect";
import { FormTextField } from "@web/components/form/FormTextField";
import { Badge } from "@web/components/ui/badge";
import { Button } from "@web/components/ui/button";
import { Card, CardContent, CardHeader } from "@web/components/ui/card";
import { Checkbox } from "@web/components/ui/checkbox";
import { Label } from "@web/components/ui/label";
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@web/components/ui/tabs";
import { toastPromise } from "@web/lib/utils";
import { trpc } from "@web/providers/Providers";
import { GripVertical, Plus, Trash } from "lucide-react";
import {
  ActionTypes,
  EventTypes,
  QuestionTypes,
} from "packages/shared/types/manage";
import { useEffect, useMemo, useState } from "react";

import { ExcelFileUpload } from "@web/components/ExcelFileUpload";
import { FormCheckBox } from "@web/components/form/FormCheckBox";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@web/components/ui/alert-dialog";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  FormProvider,
  useFieldArray,
  useForm,
  useFormContext,
} from "react-hook-form";
import { z } from "zod";

// Status enums
enum TopicStatus {
  Published = "Published",
  Draft = "Draft",
}

enum QuestionStatus {
  Published = "Published",
  Draft = "Draft",
}

// Action schema
const actionSchema = z.object({
  actionType: z.enum([
    ActionTypes.NextQuestion,
    ActionTypes.Call,
    ActionTypes.NextBehaviourQuestion,
  ]),
  event: z.enum([EventTypes.OptionSelect]),
  metadata: z.record(z.any()),
});

// Option schema
const optionSchema = z.object({
  _id: z.string().optional(),
  value: z.string(),
  prompt: z.string().optional(),
  externalId: z.string(),
  color: z.string().optional(),
  description: z.union([z.string(), z.array(z.any())]).optional(),
  endOfQuestions: z.coerce.boolean().optional().default(false),
  carePartnerValue: z.string().optional(),
});

const regularQuestionSchema = z.object({
  question: z.string(),
  externalId: z.string(),
  type: z.nativeEnum(QuestionTypes),
  status: z.nativeEnum(QuestionStatus).optional().default(QuestionStatus.Draft),
  options: z.array(
    z.object({
      value: z.string(),
      externalId: z.string(),
      prompt: z.string().optional(),
    })
  ),
});
const learnQuestionSchema = z.object({
  question: z.string(),
  externalId: z.string(),
  type: z.nativeEnum(QuestionTypes),
  status: z.nativeEnum(QuestionStatus).optional().default(QuestionStatus.Draft),
  options: z.array(
    z.object({
      value: z.string(),
      description: z.string(),
      color: z.string(),
      externalId: z.string(),
    })
  ),
});

// Question schema
const questionSchema = z.object({
  question: z.string(),
  subText: z.string().optional(),
  type: z.nativeEnum(QuestionTypes).optional().default(QuestionTypes.Select),
  externalId: z.string(),
  isOptional: z.coerce.boolean().optional().default(false),
  isSchedule: z.coerce.boolean().optional().default(false),
  duration: z.coerce.number().optional(),
  delay: z.coerce.number().optional(),
  recurringQuestion: z.coerce.boolean().optional().default(false),
  carePartnerText: z.string().optional(),
  getNextCompletedQuestion: z.string().optional(),
  actions: z.array(actionSchema).optional().default([]),
  options: z.array(optionSchema),
  status: z.nativeEnum(QuestionStatus).optional().default(QuestionStatus.Draft),
});

// Topic schema
const topicSchema = z.object({
  _id: z.string().optional(),
  name: z.string(),
  nickname: z.string(),
  category: z.array(z.string()),
  externalId: z.string(),
  tags: z.array(z.string()),
  logo: z.string().optional(),
  questions: z.array(regularQuestionSchema),
  learningQuestions: z.array(learnQuestionSchema).optional().default([]),
  dailyTrackerQuestions: z.array(questionSchema).optional().default([]),
  status: z.nativeEnum(TopicStatus).optional().default(TopicStatus.Draft),
});

type Form = z.infer<typeof topicSchema>;

export default function ManageCreate() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const topicId = searchParams.get("id");
  const isEditMode = !!topicId;

  // Topic status dialog state
  // const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [previousStatus, setPreviousStatus] = useState<TopicStatus>(
    TopicStatus.Published
  );

  // Question status dialog state
  const [showQuestionDisableDialog, setShowQuestionDisableDialog] =
    useState(false);
  const [questionPath, setQuestionPath] = useState<string>("");
  const [previousQuestionStatus, setPreviousQuestionStatus] =
    useState<QuestionStatus>(QuestionStatus.Published);

  const methods = useForm<Form>({
    resolver: zodResolver(topicSchema),
    defaultValues: {
      name: "",
      nickname: "",
      category: [],
      externalId: crypto.randomUUID(),
      tags: [],
      logo: "",
      questions: [],
      dailyTrackerQuestions: [],
      learningQuestions: [],
      status: TopicStatus.Draft,
    },
  });

  const memoizedTopicId = useMemo(() => {
    return topicId ? [topicId] : [];
  }, [topicId]);

  // Fetch topic data if in edit mode
  const { data: topicData, isLoading } = trpc.manage.getTopics.useQuery(
    { topicIds: memoizedTopicId, status: "All" },
    {
      enabled: isEditMode,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  // Fetch blog tags and categories for multi-select
  const { data: tags } = trpc.blog.tags.useQuery();
  const { data: categories, isLoading: isCategoriesLoading } =
    trpc.manage.getTopicCategories.useQuery(undefined, { initialData: [] });

  // Create/Update topic mutation
  const createTopic = trpc.manage.createTopic.useMutation();
  const updateTopic = trpc.manage.updateTopic.useMutation();

  // Track if form has been initialized to prevent resetting
  const [formInitialized, setFormInitialized] = useState(false);

  // Populate form with topic data when in edit mode (only once)
  useEffect(() => {
    console.log("🔍 FORM DEBUG: useEffect triggered", {
      isEditMode,
      hasTopicData: !!topicData,
      topicDataLength: topicData?.length,
      formInitialized,
    });

    if (isEditMode && topicData && topicData.length > 0 && !formInitialized) {
      const topic = topicData[0];

      console.log("🔍 FORM DEBUG: Topic data received", {
        topicId: topic._id,
        topicName: topic.name,
        dailyTrackerQuestionsCount: topic.dailyTrackerQuestions?.length || 0,
        rawDailyTrackerQuestions: topic.dailyTrackerQuestions,
      });

      // Process questions to convert isDeleted to status and ensure proper structure
      const processedQuestions =
        topic.questions?.map((q) => ({
          question: q.question,
          externalId: q.externalId || crypto.randomUUID(),
          type: q.type as QuestionTypes,
          options:
            q.options?.map((opt: any) => ({
              value: opt.value || "",
              externalId: opt.externalId || crypto.randomUUID(),
              prompt: opt.prompt || "",
            })) || [],
          status: q.isDeleted ? QuestionStatus.Draft : QuestionStatus.Published,
        })) || [];

      const processedDailyTrackerQuestions =
        topic.dailyTrackerQuestions?.map((q, index) => {
          console.log(
            `🔍 FORM DEBUG: Processing daily tracker question ${index}`,
            {
              questionText: q.question,
              originalOptions: q.options,
              optionsCount: q.options?.length || 0,
            }
          );

          const processedQuestion = {
            question: q.question,
            subText: q.subText || "",
            externalId: q.externalId || crypto.randomUUID(),
            type: q.type as QuestionTypes,
            isOptional: q.isOptional || false,
            isSchedule: q.isSchedule || false,
            recurringQuestion: q.recurringQuestion || false,
            delay: q.delay || 0,
            duration: q.duration || 0,
            carePartnerText: String(q.carePartnerText || ""),
            getNextCompletedQuestion: String(q.getNextCompletedQuestion || ""),
            options:
              q.options?.map((opt: any) => ({
                value: opt.value || "",
                externalId: opt.externalId || crypto.randomUUID(),
                prompt: opt.prompt || "",
                color: opt.color || "",
                description: opt.description || "",
                endOfQuestions: opt.endOfQuestions || false,
                carePartnerValue: opt.carePartnerValue || "",
              })) || [],
            actions:
              q.actions?.map((action: any) => ({
                actionType: action.actionType as ActionTypes,
                event: action.event || EventTypes.OptionSelect,
                metadata: action.metadata || {},
              })) || [],
            status: q.isDeleted
              ? QuestionStatus.Draft
              : QuestionStatus.Published,
          };

          console.log(
            `🔍 FORM DEBUG: Processed daily tracker question ${index}`,
            {
              questionText: processedQuestion.question,
              processedOptions: processedQuestion.options,
              processedOptionsCount: processedQuestion.options.length,
            }
          );

          return processedQuestion;
        }) || [];

      console.log("🔍 FORM DEBUG: All processed daily tracker questions", {
        count: processedDailyTrackerQuestions.length,
        questions: processedDailyTrackerQuestions.map((q, i) => ({
          index: i,
          question: q.question,
          optionsCount: q.options.length,
          options: q.options,
        })),
      });

      const processedLearningQuestions =
        topic.learningQuestions?.map((q) => ({
          question: q.question,
          externalId: q.externalId || crypto.randomUUID(),
          type: q.type as QuestionTypes,
          options:
            q.options?.map((opt: any) => ({
              value: opt.value || "",
              externalId: opt.externalId || crypto.randomUUID(),
              color: opt.color || "",
              description: opt.description || "",
            })) || [],
          status: q.isDeleted ? QuestionStatus.Draft : QuestionStatus.Published,
        })) || [];

      console.log(
        "🔍 FORM DEBUG: Initializing form with topic data (one time only)"
      );

      const formData = {
        _id: String(topic._id),
        name: topic.name,
        nickname: topic.nickname || topic.name,
        category: Array.isArray(topic.category)
          ? topic.category.map((c: any) =>
              typeof c === "string" ? c : c._id?.toString()
            )
          : topic.category &&
            typeof topic.category === "object" &&
            (topic.category as any)._id
          ? [(topic.category as any)._id.toString()]
          : [],
        externalId: topic.externalId || crypto.randomUUID(),
        logo: topic.logo,
        tags:
          topic.tags?.map((tag: any) =>
            typeof tag === "string" ? tag : tag._id?.toString()
          ) || [],
        questions: processedQuestions,
        dailyTrackerQuestions: processedDailyTrackerQuestions,
        learningQuestions: processedLearningQuestions,
        // Convert isDeleted to status
        status: topic.isDeleted ? TopicStatus.Draft : TopicStatus.Published,
      };

      console.log("🔍 FORM DEBUG: Form data being set", {
        dailyTrackerQuestionsCount: formData.dailyTrackerQuestions.length,
        dailyTrackerQuestions: formData.dailyTrackerQuestions.map((q, i) => ({
          index: i,
          question: q.question,
          optionsCount: q.options.length,
          options: q.options,
        })),
      });

      methods.reset(formData);

      // Verify form was set correctly
      setTimeout(() => {
        const currentFormValues = methods.getValues();
        console.log("🔍 FORM DEBUG: Form values after reset", {
          dailyTrackerQuestionsCount:
            currentFormValues.dailyTrackerQuestions?.length || 0,
          dailyTrackerQuestions:
            currentFormValues.dailyTrackerQuestions?.map(
              (q: any, i: number) => ({
                index: i,
                question: q.question,
                optionsCount: q.options?.length || 0,
                options: q.options,
              })
            ) || [],
        });
      }, 100);

      // Mark form as initialized to prevent future resets
      setFormInitialized(true);
    }
  }, [topicData, isEditMode, formInitialized, methods]);

  // Watch for topic status changes to show confirmation dialog
  // useEffect(() => {
  //   // Set a flag to indicate if we're in the initial loading period
  //   let isInitialLoading = true;

  //   // After 3 seconds, set isInitialLoading to false
  //   const timer = setTimeout(() => {
  //     isInitialLoading = false;
  //   }, 3000);

  //   const subscription = methods.watch((value, { name }) => {
  //     // Skip status change checks during initial loading period
  //     if (isInitialLoading) return;

  //     if (name === "status" && value.status === TopicStatus.Published) {
  //       setPreviousStatus(TopicStatus.Draft);
  //       setShowDisableDialog(true);
  //     }
  //   });

  //   return () => {
  //     clearTimeout(timer);
  //     subscription.unsubscribe();
  //   };
  // }, [methods]);

  // Watch for question status changes to show confirmation dialog
  useEffect(() => {
    // Set a flag to indicate if we're in the initial loading period
    let isInitialLoading = true;

    // After 3 seconds, set isInitialLoading to false
    const timer = setTimeout(() => {
      isInitialLoading = false;
    }, 3000);

    const subscription = methods.watch((_, { name }) => {
      // Skip status change checks during initial loading period
      if (isInitialLoading) return;

      // Check if the name matches a question status field pattern
      if (
        name &&
        (name.includes("questions.") ||
          name.includes("dailyTrackerQuestions.") ||
          name.includes("learningQuestions.")) &&
        name.endsWith(".status")
      ) {
        // Get the current value at this path
        const statusValue = methods.getValues(name as any);

        if (statusValue === QuestionStatus.Published) {
          // Store the path for later use
          setQuestionPath(name);
          setPreviousQuestionStatus(QuestionStatus.Draft);
          setShowQuestionDisableDialog(true);
        }
      }
    });

    return () => {
      clearTimeout(timer);
      subscription.unsubscribe();
    };
  }, [methods]);

  // Handle form submission
  const handleSubmit = async (data: Form) => {
    try {
      // Process questions to convert status to isDeleted
      const processedData = {
        ...data,
        logo: data.logo || "", // Ensure logo is included
        tags: data.tags.map((tag) => tag.toString()),
        // Process questions
        questions: data.questions.map((q) => ({
          ...q,
          isDeleted: q.status === QuestionStatus.Draft,
        })),
        // Process learning questions
        learningQuestions: data.learningQuestions.map((q) => ({
          ...q,
          isDeleted: q.status === QuestionStatus.Draft,
        })),
        // Process daily tracker questions
        dailyTrackerQuestions: data.dailyTrackerQuestions.map((q) => ({
          ...q,
          isDeleted: q.status === QuestionStatus.Draft,
        })),
      };

      if (isEditMode) {
        // Make sure _id is present for update
        if (!processedData._id) {
          console.error("Missing _id for update operation");
          return;
        }

        await toastPromise({
          asyncFunc: updateTopic.mutateAsync({
            _id: processedData._id,
            name: processedData.name,
            nickname: processedData.nickname,
            category: processedData.category,
            externalId: processedData.externalId,
            logo: processedData.logo,
            tags: processedData.tags.map((tag) => tag.toString()),
            questions: processedData.questions,
            learningQuestions: processedData.learningQuestions,
            dailyTrackerQuestions: processedData.dailyTrackerQuestions,
            // Convert topic status to isDeleted
            isDeleted: processedData.status === TopicStatus.Draft,
          } as any), // Use type assertion to bypass TypeScript error
          success: "Topic updated successfully",
          error: "Failed to update topic",
          onSuccess: () => {
            router.push("/manage");
          },
        });
      } else {
        await toastPromise({
          asyncFunc: createTopic.mutateAsync({
            ...processedData,
            category: processedData.category,
            logo: processedData.logo,
            tags: processedData.tags.map((tag) => tag.toString()),
            // Convert topic status to isDeleted
            isDeleted: processedData.status === TopicStatus.Draft,
          } as any), // Use type assertion to bypass TypeScript error
          success: "Topic created successfully",
          error: "Failed to create topic",
          onSuccess: () => {
            router.push("/manage");
          },
        });
      }
    } catch (error) {
      console.error("Error saving topic:", error);
    }
  };

  // Subscription questions field array
  const {
    fields: questions,
    append: appendQuestion,
    remove: removeQuestion,
  } = useFieldArray({
    name: "questions",
    control: methods.control,
  });

  // Daily tracker questions field array
  const {
    fields: dailyTrackerQuestions,
    append: appendDailyTrackerQuestion,
    remove: removeDailyTrackerQuestion,
  } = useFieldArray({
    control: methods.control,
    name: "dailyTrackerQuestions",
  });

  // Learning questions field array
  const {
    fields: learningQuestions,
    append: appendLearningQuestion,
    remove: removeLearningQuestion,
  } = useFieldArray({
    control: methods.control,
    name: "learningQuestions",
  });

  // Parse Subscription Questions from Excel data
  const parseSubscriptionQuestions = (sheetData: any[][]) => {
    const questions: any[] = [];
    const questionMap = new Map();

    // Skip header row (index 0)
    for (let i = 1; i < sheetData.length; i++) {
      const row = sheetData[i];
      const [
        topicName,
        questionText,
        questionExternalId,
        questionType,
        status,
        optionText,
        optionExternalId,
        prompt,
      ] = row;

      if (!questionMap.has(questionExternalId)) {
        // Map Excel question type to proper enum value (case insensitive)
        let mappedQuestionType = QuestionTypes.Select; // default
        if (questionType) {
          const normalizedType = questionType.toLowerCase();
          if (normalizedType === "select") {
            mappedQuestionType = QuestionTypes.Select;
          } else if (normalizedType === "multiselect") {
            mappedQuestionType = QuestionTypes.MultiSelect;
          }
        }

        questionMap.set(questionExternalId, {
          question: questionText,
          externalId: questionExternalId,
          type: mappedQuestionType,
          status:
            status === "Published"
              ? QuestionStatus.Published
              : QuestionStatus.Draft,
          options: [],
        });
      }

      const question = questionMap.get(questionExternalId);
      question.options.push({
        value: optionText,
        externalId: optionExternalId,
        prompt: prompt || "",
      });
    }

    return Array.from(questionMap.values());
  };

  // Parse Daily Tracker Questions from Excel data
  const parseDailyTrackerQuestions = (sheetData: any[][]) => {
    const questions: any[] = [];
    const questionMap = new Map();

    // Skip header row (index 0)
    for (let i = 1; i < sheetData.length; i++) {
      const row = sheetData[i];
      const [
        topicName,
        questionText,
        questionExternalId,
        subText,
        questionType,
        carePartnerText,
        status,
        isOptional,
        isSchedule,
        recurringQuestion,
        delay,
        duration,
        optionText,
        optionExternalId,
        prompt,
        color,
        carePartnerValue,
        description,
        endOfQuestions,
        actionType,
        eventType,
        nextQuestion,
        selectedQuestionId,
        chosenFrequency,
        trackingWindow,
        phoneNumber,
      ] = row;

      if (!questionMap.has(questionExternalId)) {
        // Map Excel question type to proper enum value
        let mappedQuestionType = QuestionTypes.Select; // default
        if (questionType) {
          const normalizedType = questionType.toLowerCase();
          if (normalizedType === "select") {
            mappedQuestionType = QuestionTypes.Select;
          } else if (normalizedType === "multiselect") {
            mappedQuestionType = QuestionTypes.MultiSelect;
          } else if (normalizedType === "slider") {
            mappedQuestionType = QuestionTypes.Slider;
          } else if (normalizedType === "horizontalselect") {
            mappedQuestionType = QuestionTypes.HorizontalSelect;
          } else if (normalizedType === "emergency") {
            mappedQuestionType = QuestionTypes.Emergency;
          }
        }

        questionMap.set(questionExternalId, {
          question: questionText,
          subText: subText || "",
          type: mappedQuestionType,
          externalId: questionExternalId,
          isOptional: isOptional === "Yes",
          isSchedule: isSchedule === "Yes",
          duration: duration ? parseInt(duration) : undefined,
          delay: delay ? parseInt(delay) : undefined,
          recurringQuestion: recurringQuestion === "Yes",
          carePartnerText: carePartnerText || "",
          getNextCompletedQuestion: "",
          actions: [],
          options: [],
          status:
            status === "Published"
              ? QuestionStatus.Published
              : QuestionStatus.Draft,
        });
      }

      const question = questionMap.get(questionExternalId);

      // Add option
      if (optionText) {
        question.options.push({
          _id: undefined,
          value: optionText,
          prompt: prompt || "",
          externalId: optionExternalId,
          color: color || "",
          description: description || "",
          endOfQuestions: endOfQuestions === "Yes",
          carePartnerValue: carePartnerValue || "",
        });
      }

      // Add action if present
      if (actionType && eventType && optionExternalId) {
        // Map action type from Excel to proper enum
        let mappedActionType = ActionTypes.NextQuestion; // default
        if (actionType) {
          const normalizedActionType = actionType
            .toLowerCase()
            .replace(/\s+/g, "");
          if (normalizedActionType === "call") {
            mappedActionType = ActionTypes.Call;
          } else if (normalizedActionType === "nextquestion") {
            mappedActionType = ActionTypes.NextQuestion;
          } else if (normalizedActionType === "nextbehaviourquestion") {
            mappedActionType = ActionTypes.NextBehaviourQuestion;
          }
        }

        // Build metadata according to the correct structure
        const metadata: any = {
          optionExternalId: optionExternalId,
          nextQuestionExternalId: selectedQuestionId || "", // This should be the external ID of the next question
        };

        // Add additional fields for NextBehaviourQuestion and Call actions
        if (
          mappedActionType === ActionTypes.NextBehaviourQuestion ||
          mappedActionType === ActionTypes.Call
        ) {
          if (chosenFrequency) {
            metadata.choosenFrequency = chosenFrequency;
          }
          if (trackingWindow) {
            metadata.trackingWindow = trackingWindow;
          }
        }

        // Add phone number for Call actions
        if (mappedActionType === ActionTypes.Call && phoneNumber) {
          metadata.phone = phoneNumber;
        }

        const action = {
          actionType: mappedActionType,
          event: EventTypes.OptionSelect,
          metadata: metadata,
        };

        // Create a unique key for the action to avoid duplicates
        const actionKey = `${action.actionType}-${action.metadata.optionExternalId}-${action.metadata.nextQuestionExternalId}`;

        // Check if this specific action already exists
        const existingAction = question.actions.find(
          (a: any) =>
            `${a.actionType}-${a.metadata?.optionExternalId || ""}-${
              a.metadata?.nextQuestionExternalId || ""
            }` === actionKey
        );

        if (!existingAction) {
          question.actions.push(action);
        }
      }
    }

    const finalQuestions = Array.from(questionMap.values());
    return finalQuestions;
  };

  // Parse Learning Questions from Excel data
  const parseLearningQuestions = (sheetData: any[][]) => {
    const questions: any[] = [];
    const questionMap = new Map();

    // Skip header row (index 0)
    for (let i = 1; i < sheetData.length; i++) {
      const row = sheetData[i];
      const [
        topicName,
        questionText,
        questionExternalId,
        questionType,
        status,
        optionText,
        optionExternalId,
        color,
        description,
      ] = row;

      if (!questionMap.has(questionExternalId)) {
        questionMap.set(questionExternalId, {
          question: questionText,
          externalId: questionExternalId,
          type: QuestionTypes.Select, // Learning questions are always Select
          status:
            status === "Published"
              ? QuestionStatus.Published
              : QuestionStatus.Draft,
          options: [],
        });
      }

      const question = questionMap.get(questionExternalId);
      question.options.push({
        value: optionText,
        description: description || "",
        color: color || "#ACACAC",
        externalId: optionExternalId,
      });
    }

    return Array.from(questionMap.values());
  };

  // Excel data import function
  const importExcelData = (excelData: any) => {
    try {
      // Parse Subscription Questions
      const subscriptionSheet = excelData.sheets["Subscription Questions"];
      const subscriptionQuestions =
        parseSubscriptionQuestions(subscriptionSheet);

      // Parse Daily Tracker Questions
      const dailyTrackerSheet = excelData.sheets["Daily Tracker Questions"];
      const dailyTrackerQuestions =
        parseDailyTrackerQuestions(dailyTrackerSheet);

      // Parse Learning Questions
      const learningSheet = excelData.sheets["Learning Questions"];
      const learningQuestionsData = parseLearningQuestions(learningSheet);

      // Get topic name from first sheet
      const topicName = subscriptionSheet[1]?.[0] || "Imported Topic";

      // Update form with parsed data
      methods.setValue("name", topicName);
      methods.setValue("nickname", topicName);
      methods.setValue("questions", subscriptionQuestions);
      methods.setValue("dailyTrackerQuestions", dailyTrackerQuestions);
      methods.setValue("learningQuestions", learningQuestionsData);

      alert(`Successfully imported data for topic: ${topicName}`);
    } catch (error) {
      console.error("Error importing Excel data:", error);
      alert(
        "Error importing Excel data. Please check the console for details."
      );
    }
  };

  if (isLoading && isEditMode) {
    return (
      <div className="container py-6">
        <div className="flex justify-center py-8">Loading topic data...</div>
      </div>
    );
  }

  return (
    <div className="container py-6">
      <h1 className="text-2xl font-bold mb-6">
        {isEditMode ? "Edit Topic" : "Create Topic"}
      </h1>

      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleSubmit)}>
          <Tabs defaultValue="basic">
            <TabsList className="mb-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="questions">
                Subscription Questions
              </TabsTrigger>
              <TabsTrigger value="dailyTracker">
                Daily Tracker Questions
              </TabsTrigger>
              <TabsTrigger value="learning">Learning Questions</TabsTrigger>
            </TabsList>

            {/* Basic Info Tab */}
            <TabsContent value="basic" className="space-y-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  name="name"
                  label="Topic Name"
                  placeholder="Enter topic name"
                />
                <FormField
                  name="nickname"
                  label="Nickname"
                  placeholder="Enter nickname"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <FormMultiSelect
                    name="category"
                    label="Categories"
                    placeholder={
                      isCategoriesLoading
                        ? "Loading categories..."
                        : "Select categories"
                    }
                    options={
                      categories?.map((category) => ({
                        label: category.name,
                        value: String(category._id),
                      })) || []
                    }
                    creatable={false}
                    loadingIndicator={
                      <div className="text-center py-2 text-sm">
                        Loading categories...
                      </div>
                    }
                    emptyIndicator={
                      <div className="text-center py-2 text-sm">
                        No categories found. Please create categories first.
                      </div>
                    }
                    disabled={isCategoriesLoading}
                  />
                </div>
                <FormField
                  name="externalId"
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormSelect
                  name="status"
                  label="Status"
                  options={[
                    {
                      label: TopicStatus.Published,
                      value: TopicStatus.Published,
                    },
                    {
                      label: TopicStatus.Draft,
                      value: TopicStatus.Draft,
                    },
                  ]}
                />
              </div>

              {/* Confirmation Dialog for Publishing Topic */}
              {/* <AlertDialog
                open={showDisableDialog}
                onOpenChange={setShowDisableDialog}
              >
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Confirm Publishing Topic
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      You are changing this topic from Draft to Published
                      status. Published topics will be visible to users. Are you
                      sure you want to continue?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel
                      onClick={() => {
                        methods.setValue("status", previousStatus);
                      }}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction>Confirm</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog> */}

              {/* Confirmation Dialog for Publishing Question */}
              <AlertDialog
                open={showQuestionDisableDialog}
                onOpenChange={setShowQuestionDisableDialog}
              >
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Confirm Publishing Question
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      You are changing this question from Draft to Published
                      status. Published questions will be visible to users. Are
                      you sure you want to continue?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel
                      onClick={() => {
                        // Reset the question status to Draft
                        if (questionPath) {
                          methods.setValue(
                            questionPath as any,
                            previousQuestionStatus
                          );
                        }
                      }}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction>Confirm</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <div>
                <FormMultiSelect
                  name="tags"
                  label="Tag"
                  placeholder="Select tags"
                  options={
                    tags?.map((tag) => ({
                      label: tag.tag,
                      value: String(tag._id),
                    })) || []
                  }
                />
              </div>

              {/* Excel File Upload Section */}
              <div className="mt-6">
                <ExcelFileUpload
                  onDataParsed={(data) => {
                    importExcelData(data);
                  }}
                  onError={(error) => {
                    console.error("Excel upload error:", error);
                    alert(`Error: ${error}`);
                  }}
                />
              </div>
            </TabsContent>

            {/* Subscription Questions Tab */}
            <TabsContent value="questions" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  Subscription Questions
                </h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendQuestion({
                      question: "",
                      type: QuestionTypes.Select,
                      options: [],
                      externalId: crypto.randomUUID(),
                      status: QuestionStatus.Draft,
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Subscription Question
                </Button>
              </div>

              {questions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No questions added yet. Click "Add Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((field, index) => {
                    const questionText =
                      methods.watch(`questions.${index}.question`) ||
                      "New Question";
                    const title = `Question ${index + 1}: ${questionText}`;

                    return (
                      <CollapsibleCard
                        key={field.id}
                        title={title}
                        onDelete={() => removeQuestion(index)}
                        // disableDelete={isEditMode}
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`questions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`questions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                            disabled
                          />
                          <FormSelect
                            name={`questions.${index}.type`}
                            label="Question Type (Only Select & MultiSelect is supported)"
                            options={[
                              {
                                label: QuestionTypes.Select,
                                value: QuestionTypes.Select,
                              },
                              {
                                label: QuestionTypes.MultiSelect,
                                value: QuestionTypes.MultiSelect,
                              },
                            ]}
                          />
                          <FormSelect
                            name={`questions.${index}.status`}
                            label="Status"
                            options={[
                              {
                                label: QuestionStatus.Published,
                                value: QuestionStatus.Published,
                              },
                              {
                                label: QuestionStatus.Draft,
                                value: QuestionStatus.Draft,
                              },
                            ]}
                          />
                        </div>

                        <RegularQuestionOptions questionIndex={index} />
                      </CollapsibleCard>
                    );
                  })}
                </div>
              )}
            </TabsContent>

            {/* Learning Questions Tab */}
            <TabsContent value="learning" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Learning Questions</h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendLearningQuestion({
                      question: "",
                      externalId: crypto.randomUUID(),
                      type: QuestionTypes.Select,
                      options: [],
                      status: QuestionStatus.Draft,
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Learning Question
                </Button>
              </div>

              {learningQuestions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No learning questions added yet. Click "Add Learning
                    Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {learningQuestions.map((field, index) => {
                    const questionText =
                      methods.watch(`learningQuestions.${index}.question`) ||
                      "New Learning Question";
                    const title = `Learning Question ${
                      index + 1
                    }: ${questionText}`;

                    return (
                      <CollapsibleCard
                        key={field.id}
                        title={title}
                        onDelete={() => removeLearningQuestion(index)}
                        disableDelete={isEditMode}
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`learningQuestions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`learningQuestions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                            disabled
                          />
                          <FormSelect
                            name={`learningQuestions.${index}.type`}
                            disabled
                            label="Question Type (Only Select is supported)"
                            options={Object.values(QuestionTypes).map(
                              (type) => ({
                                label: type,
                                value: type,
                              })
                            )}
                          />
                          <FormSelect
                            name={`learningQuestions.${index}.status`}
                            label="Status"
                            options={[
                              {
                                label: QuestionStatus.Published,
                                value: QuestionStatus.Published,
                              },
                              {
                                label: QuestionStatus.Draft,
                                value: QuestionStatus.Draft,
                              },
                            ]}
                          />
                        </div>

                        <LearnQuestionOptions
                          questionIndex={index}
                          questionType="learningQuestions"
                        />
                      </CollapsibleCard>
                    );
                  })}
                </div>
              )}
            </TabsContent>

            {/* Daily Tracker Questions Tab */}
            <TabsContent value="dailyTracker" className="py-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">
                  Daily Tracker Questions
                </h2>
                <Button
                  type="button"
                  onClick={() =>
                    appendDailyTrackerQuestion({
                      question: "",
                      type: QuestionTypes.Select,
                      isOptional: false,
                      isSchedule: false,
                      recurringQuestion: false,
                      externalId: crypto.randomUUID(),
                      options: [],
                      actions: [],
                      status: QuestionStatus.Draft,
                    })
                  }
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Daily Tracker Question
                </Button>
              </div>

              {dailyTrackerQuestions.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground">
                    No daily tracker questions added yet. Click "Add Daily
                    Tracker Question" to get started.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {dailyTrackerQuestions.map((field, index) => {
                    const questionText =
                      methods.watch(
                        `dailyTrackerQuestions.${index}.question`
                      ) || "Untitled Question";

                    // Get question metadata
                    const isOptional = methods.watch(
                      `dailyTrackerQuestions.${index}.isOptional`
                    );
                    const isSchedule = methods.watch(
                      `dailyTrackerQuestions.${index}.isSchedule`
                    );
                    const recurringQuestion = methods.watch(
                      `dailyTrackerQuestions.${index}.recurringQuestion`
                    );
                    const delay = methods.watch(
                      `dailyTrackerQuestions.${index}.delay`
                    );
                    const duration = methods.watch(
                      `dailyTrackerQuestions.${index}.duration`
                    );

                    return (
                      <CollapsibleCard
                        key={field.id}
                        title={questionText}
                        onDelete={() => removeDailyTrackerQuestion(index)}
                        disableDelete={isEditMode}
                        customHeader={
                          <div className="flex flex-col gap-2">
                            <div className="font-medium">{questionText}</div>
                            <div className="flex flex-wrap gap-2">
                              {isOptional && (
                                <Badge variant="secondary">Optional</Badge>
                              )}
                              {recurringQuestion && (
                                <Badge
                                  variant="default"
                                  className="bg-blue-500 hover:bg-blue-600"
                                >
                                  Recurring
                                </Badge>
                              )}
                              {delay && delay > 0 && (
                                <Badge
                                  variant="default"
                                  className="bg-orange-500 hover:bg-orange-600"
                                >
                                  Delay: {delay} day{delay !== 1 ? "s" : ""}
                                </Badge>
                              )}
                              {isSchedule && (
                                <Badge variant="outline">
                                  Scheduled{duration ? ` (${duration}d)` : ""}
                                </Badge>
                              )}
                            </div>
                          </div>
                        }
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            name={`dailyTrackerQuestions.${index}.question`}
                            label="Question Text"
                            placeholder="Enter question"
                          />
                          <FormField
                            name={`dailyTrackerQuestions.${index}.externalId`}
                            label="External ID"
                            placeholder="Enter external ID"
                            disabled
                          />
                        </div>

                        <FormField
                          name={`dailyTrackerQuestions.${index}.subText`}
                          label="Sub Text (Optional)"
                          placeholder="Enter sub text"
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormSelect
                            name={`dailyTrackerQuestions.${index}.type`}
                            label="Question Type"
                            options={Object.values(QuestionTypes).map(
                              (type) => ({
                                label: type,
                                value: type,
                              })
                            )}
                          />

                          <FormField
                            name={`dailyTrackerQuestions.${index}.carePartnerText`}
                            label="Care Partner Text (Optional)"
                            placeholder="Enter care partner text"
                          />

                          <FormSelect
                            name={`dailyTrackerQuestions.${index}.status`}
                            label="Status"
                            options={[
                              {
                                label: QuestionStatus.Published,
                                value: QuestionStatus.Published,
                              },
                              {
                                label: QuestionStatus.Draft,
                                value: QuestionStatus.Draft,
                              },
                            ]}
                          />
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <FormCheckBox
                              name={`dailyTrackerQuestions.${index}.isOptional`}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.isOptional`}
                            >
                              Optional Question
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <FormCheckBox
                              name={`dailyTrackerQuestions.${index}.isSchedule`}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.isSchedule`}
                            >
                              Scheduled Question
                            </Label>
                          </div>

                          <div className="flex items-center space-x-2">
                            <FormCheckBox
                              name={`dailyTrackerQuestions.${index}.recurringQuestion`}
                            />
                            <Label
                              htmlFor={`dailyTrackerQuestions.${index}.recurringQuestion`}
                            >
                              Recurring Question
                            </Label>
                          </div>
                        </div>

                        {methods.watch(
                          `dailyTrackerQuestions.${index}.isSchedule` as any
                        ) && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              name={`dailyTrackerQuestions.${index}.delay`}
                              label="Delay (days)"
                              type="number"
                              placeholder="Enter delay in days"
                            />
                            <FormField
                              name={`dailyTrackerQuestions.${index}.duration`}
                              label="Duration (days)"
                              type="number"
                              placeholder="Enter duration in days"
                            />
                          </div>
                        )}

                        <QuestionOptions
                          questionIndex={index}
                          questionType="dailyTrackerQuestions"
                        />
                      </CollapsibleCard>
                    );
                  })}
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="mt-6 flex justify-end items-center">
            <div className="flex space-x-4">
              <Button type="button" onClick={() => router.push("/manage")}>
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-8"
                disabled={createTopic.isLoading || updateTopic.isLoading}
              >
                {isEditMode ? "Update Topic" : "Create Topic"}
              </Button>
            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}

// Define types for our components
interface QuestionOptionsProps {
  questionIndex: number;
  questionType?: string;
}

interface OptionActionsProps {
  questionIndex: number;
  optionIndex: number;
  questionType?: string;
}

interface ActionMetadataProps {
  questionIndex: number;
  optionIndex: number;
  actionIndex: number;
  questionType?: string;
}

interface CollapsibleCardProps {
  title: string;
  children: React.ReactNode;
  onDelete?: () => void;
  disableDelete?: boolean;
  customHeader?: React.ReactNode;
}

// Collapsible Card Component
function CollapsibleCard({
  title,
  children,
  onDelete,
  disableDelete,
  customHeader,
}: CollapsibleCardProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <Card className="border-primary">
      <CardHeader
        className="flex flex-row items-center justify-between p-4 pb-2 cursor-pointer"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center flex-1">
          <GripVertical
            className="h-5 w-5 text-muted-foreground mr-2 cursor-grab"
            onClick={(e) => e.stopPropagation()}
          />
          {customHeader ? (
            customHeader
          ) : (
            <h3 className="font-medium">{title}</h3>
          )}
        </div>
        <div className="flex items-center">
          {onDelete && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              disabled={disableDelete}
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash className="h-4 w-4" />
            </Button>
          )}
          {isCollapsed ? (
            <ChevronDown className="h-5 w-5 ml-2" />
          ) : (
            <ChevronUp className="h-5 w-5 ml-2" />
          )}
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="p-4 pt-0 space-y-4">{children}</CardContent>
      )}
    </Card>
  );
}

// Collapsible Option Card Component
function CollapsibleOptionCard({
  title,
  children,
  onDelete,
  disableDelete,
}: CollapsibleCardProps) {
  const [isCollapsed, setIsCollapsed] = useState(true);

  return (
    <Card className="border-gray-200">
      <div
        className="flex flex-row items-center justify-between p-3 cursor-pointer"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center">
          <GripVertical
            className="h-4 w-4 text-muted-foreground mr-2 cursor-grab"
            onClick={(e) => e.stopPropagation()}
          />
          <h5 className="text-sm font-medium">{title}</h5>
        </div>
        <div className="flex items-center">
          {onDelete && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              // disabled={disableDelete}
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            >
              <Trash className="h-3 w-3" />
            </Button>
          )}
          {isCollapsed ? (
            <ChevronDown className="h-4 w-4 ml-1" />
          ) : (
            <ChevronUp className="h-4 w-4 ml-1" />
          )}
        </div>
      </div>

      {!isCollapsed && (
        <CardContent className="p-3 pt-0 space-y-3">{children}</CardContent>
      )}
    </Card>
  );
}

function QuestionOptions({
  questionIndex,
  questionType = "questions",
}: QuestionOptionsProps) {
  const { control, watch } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => {
          const optionText =
            watch(
              `${questionType}.${questionIndex}.options.${optionIndex}.value`
            ) || "New Option";
          const title = `Option ${optionIndex + 1}: ${optionText}`;

          return (
            <CollapsibleOptionCard
              key={option.id}
              title={title}
              onDelete={() => removeOption(optionIndex)}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.prompt`}
                  label="Prompt (Optional)"
                  placeholder="Enter prompt text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.color`}
                  label="Color (Optional)"
                  placeholder="e.g. #FF0000"
                  type="color"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.carePartnerValue`}
                  label="Care Partner Value (Optional)"
                  placeholder="Enter care partner value"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.description`}
                  label="Description (Optional)"
                  placeholder="Enter description"
                />
              </div>

              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions`}
                    {...control.register(
                      `${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions` as any
                    )}
                  />
                  <label
                    htmlFor={`${questionType}.${questionIndex}.options.${optionIndex}.endOfQuestions`}
                    className="text-sm"
                  >
                    End of Questions
                  </label>
                </div>
              </div>

              <OptionActions
                questionIndex={questionIndex}
                optionIndex={optionIndex}
                questionType={questionType}
              />
            </CollapsibleOptionCard>
          );
        })
      )}
    </div>
  );
}

function RegularQuestionOptions({
  questionIndex,
  questionType = "questions",
}: QuestionOptionsProps) {
  const { control, watch } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => {
          const optionText =
            watch(
              `${questionType}.${questionIndex}.options.${optionIndex}.value`
            ) || "New Option";
          const title = `Option ${optionIndex + 1}: ${optionText}`;

          return (
            <CollapsibleOptionCard
              key={option.id}
              title={title}
              onDelete={() => removeOption(optionIndex)}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.prompt`}
                  label="Prompt (Optional)"
                  placeholder="Enter prompt text"
                />
              </div>
            </CollapsibleOptionCard>
          );
        })
      )}
    </div>
  );
}

function LearnQuestionOptions({
  questionIndex,
  questionType = "learningQuestions",
}: QuestionOptionsProps) {
  const { control, watch } = useFormContext();

  const {
    fields: options,
    append: appendOption,
    remove: removeOption,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.options` as any,
  });

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">Options</h4>
        <Button
          type="button"
          size="sm"
          onClick={() =>
            appendOption({
              value: "",
              color: "",
              externalId: crypto.randomUUID(),
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Option
        </Button>
      </div>

      {options.length === 0 ? (
        <div className="text-center p-4 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No options added yet. Click "Add Option" to get started.
          </p>
        </div>
      ) : (
        options.map((option, optionIndex) => {
          const optionText =
            watch(
              `${questionType}.${questionIndex}.options.${optionIndex}.value`
            ) || "New Option";
          const title = `Option ${optionIndex + 1}: ${optionText}`;

          return (
            <CollapsibleOptionCard
              key={option.id}
              title={title}
              onDelete={() => removeOption(optionIndex)}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.value`}
                  label="Option Text"
                  placeholder="Enter option text"
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.externalId`}
                  label="External ID"
                  placeholder="Enter external ID"
                  disabled
                />

                <FormField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.color`}
                  label="Color"
                  placeholder="Select Color"
                  type="color"
                />

                <FormTextField
                  name={`${questionType}.${questionIndex}.options.${optionIndex}.description`}
                  label="Description"
                  placeholder="Enter prompt text"
                />
              </div>
            </CollapsibleOptionCard>
          );
        })
      )}
    </div>
  );
}

function OptionActions({
  questionIndex,
  optionIndex,
  questionType = "questions",
}: OptionActionsProps) {
  const { control, watch } = useFormContext();
  const currentOptionExternalId = watch(
    `${questionType}.${questionIndex}.options.${optionIndex}.externalId`
  );

  const {
    fields: actions,
    append: appendAction,
    remove: removeAction,
  } = useFieldArray({
    control,
    name: `${questionType}.${questionIndex}.actions` as any,
  });

  // Get indices of actions that are related to this option
  const optionActionIndices: number[] = [];
  for (let i = 0; i < actions.length; i++) {
    const optionExternalId = watch(
      `${questionType}.${questionIndex}.actions.${i}.metadata.optionExternalId`
    );
    if (
      optionExternalId === currentOptionExternalId ||
      optionExternalId === "*"
    ) {
      optionActionIndices.push(i);
    }
  }

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h5 className="text-sm font-medium">Actions</h5>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() =>
            appendAction({
              actionType: ActionTypes.NextQuestion,
              event: EventTypes.OptionSelect,
              metadata: {
                optionExternalId: "*",
              },
            })
          }
        >
          <Plus className="mr-2 h-3 w-3" /> Add Action
        </Button>
      </div>

      {optionActionIndices.length === 0 ? (
        <div className="text-center p-3 border border-dashed rounded-lg">
          <p className="text-muted-foreground text-sm">
            No actions added yet. Click "Add Action" to get started.
          </p>
        </div>
      ) : (
        optionActionIndices.map((actionIndex, index) => (
          <div key={index} className="border rounded-md p-3 space-y-3">
            <div className="flex justify-between items-center">
              <h6 className="text-xs font-medium">Action {index + 1}</h6>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeAction(actionIndex)}
              >
                <Trash className="h-3 w-3" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <FormSelect
                name={`${questionType}.${questionIndex}.actions.${actionIndex}.actionType`}
                label="Action Type"
                options={[
                  {
                    label: "Next Question",
                    value: ActionTypes.NextQuestion,
                  },
                  {
                    label: "Next Behaviour Question",
                    value: ActionTypes.NextBehaviourQuestion,
                  },
                  {
                    label: "Call",
                    value: ActionTypes.Call,
                  },
                ]}
              />

              <FormSelect
                name={`${questionType}.${questionIndex}.actions.${actionIndex}.event`}
                label="Event Type"
                options={[
                  {
                    label: "Option Select",
                    value: EventTypes.OptionSelect,
                  },
                  {
                    label: "On Inactive Schedule",
                    value: EventTypes.OnInactiveSchedule,
                  },
                ]}
              />
            </div>

            <ActionMetadata
              questionIndex={questionIndex}
              optionIndex={optionIndex}
              actionIndex={actionIndex}
              questionType={questionType}
            />
          </div>
        ))
      )}
    </div>
  );
}

function ActionMetadata({
  questionIndex,
  optionIndex,
  actionIndex,
  questionType = "questions",
}: ActionMetadataProps) {
  const { watch } = useFormContext();
  const currentOptionExternalId = watch(
    `${questionType}.${questionIndex}.options.${optionIndex}.externalId`
  );

  const actionType = watch(
    `${questionType}.${questionIndex}.actions.${actionIndex}.actionType` as any
  );

  if (actionType === ActionTypes.NextQuestion) {
    const { watch } = useFormContext();
    const allQuestions = [
      ...(watch("questions") || []),
      ...(watch("dailyTrackerQuestions") || []),
      ...(watch("learningQuestions") || []),
    ];

    // No need to get the selected next question for this view

    return (
      <div className="space-y-3">
        <FormSelect
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.optionExternalId`}
          label="On Option Selected"
          options={[
            { label: "Any Option (*)", value: "*" },
            { label: "Current Option", value: currentOptionExternalId },
          ]}
        />
        <FormSelect
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.nextQuestionExternalId`}
          label="Next Question"
          placeholder="Select next question"
          options={allQuestions.map((q) => ({
            label: q.question || "Unnamed Question",
            value: q.externalId,
          }))}
        />
      </div>
    );
  }

  if (actionType === ActionTypes.NextBehaviourQuestion) {
    const { watch } = useFormContext();
    const allQuestions = [
      ...(watch("questions") || []),
      ...(watch("dailyTrackerQuestions") || []),
      ...(watch("learningQuestions") || []),
    ];

    // No need to get the selected next question for this view

    return (
      <div className="space-y-3">
        <FormSelect
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.optionExternalId`}
          label="On Option Selected"
          options={[
            { label: "Any Option (*)", value: "*" },
            { label: "Current Option", value: currentOptionExternalId },
          ]}
        />
        <FormSelect
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.nextQuestionExternalId`}
          label="Next Question"
          placeholder="Select next question"
          options={allQuestions.map((q) => ({
            label: q.question || "Unnamed Question",
            value: q.externalId,
          }))}
        />
        <FormField
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.choosenFrequency`}
          label="Chosen Frequency"
          placeholder="Enter chosen frequency"
        />
        <FormField
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.trackingWindow`}
          label="Tracking Window"
          placeholder="Enter tracking window"
          type="number"
        />
      </div>
    );
  }

  if (actionType === ActionTypes.Call) {
    return (
      <div className="space-y-3">
        <FormSelect
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.optionExternalId`}
          label="On Option Selected"
          options={[
            { label: "Any Option (*)", value: "*" },
            { label: "Current Option", value: currentOptionExternalId },
          ]}
        />
        <FormField
          name={`${questionType}.${questionIndex}.actions.${actionIndex}.metadata.phone`}
          label="Phone Number"
          placeholder="Enter phone number"
        />
      </div>
    );
  }

  return (
    <div className="p-3 text-center text-sm text-muted-foreground">
      Select an action type to see additional options
    </div>
  );
}
